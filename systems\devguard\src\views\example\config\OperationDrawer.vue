<template>
  <Drawer
    :open="show"
    width="900px"
    :closable="false"
    placement="right"
    @afterOpenChange="v => !v && modalDestroy()"
    @close="() => modalConfirm()"
  >
    <div v-if="gridOptions.data?.length">
      <FilterWrapper
        v-model:form="searchForm"
        class="mb-12px"
      />
      <BasicVxeTable :options="gridOptions" />
      <div class="flex justify-end p-4">
        <div class="w-100% flex items-center justify-between">
          <div>
            共计
            {{ pagerConfig.total }}
            条数据
          </div>
          <Pagination
            v-model:current="pagerConfig.currentPage"
            v-model:pageSize="pagerConfig.pageSize"
            :total="pagerConfig.total"
            showSizeChanger
            :pageSizeOptions="['10', '20', '50', '100']"
            @change="handlePageData"
          />
        </div>
      </div>
    </div>

    <div v-else class="h-full flex items-center justify-center">
      <Empty :image="emptyImg" description="暂无数据" />
    </div>

    <template #title>
      <div class="flex items-center justify-between">
        操作历史
      </div>
    </template>
    <template #footer>
      <div class="flex justify-end">
        <Button @click="close">
          关闭
        </Button>
      </div>
    </template>
  </Drawer>
</template>

<script lang="tsx" setup>
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import { Button, Drawer, Empty, Pagination } from 'ant-design-vue';
import { FilterWrapper } from './filter-wrapper.tsx';

import { onMounted, reactive, ref } from 'vue';

import dayjs from 'dayjs';
import { type VxeGridProps, BasicVxeTable } from '@hg-tech/oasis-common';

const props = defineProps<ModalBaseProps & {

}>();
interface RowVO {
  returnTime?: string;
  deviceName?: string;
  assetNo?: string;
  usedTime?: string;
  UpdatedAt?: string;
}

const emptyImg = Empty.PRESENTED_IMAGE_SIMPLE;
const pagerConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 20,
});
const searchForm = ref();
const gridOptions = reactive<VxeGridProps<RowVO>>({
  showOverflow: true,
  maxHeight: '100%',
  border: 'none',
  columns: [
    { field: 'point', title: '', width: '30px', align: 'center', slots: { default() {
      return <div class="h-6px w-6px rd-half bg-FO-Container-Fill5" />;
    } } },
    { field: 'returnTime', title: '完成时间', width: '170px', align: 'left', slots: { default({ row }) {
      return dayjs(row.UpdatedAt).format('YYYY-MM-DD HH:mm:ss');
    } } },
    { field: 'deviceName', title: '操作人', align: 'left', slots: { default({ row }) {
      return (
        <span>
          <span>{row}</span>
        </span>
      );
    } } },
    { field: 'assetNo', title: '操作详情', align: 'left', slots: { default({ row }) {
      return row;
    } } },
  ],
  data: [],
  columnConfig: {
    resizable: false,
  },
});
async function handlePageData() {
  try {
    // gridOptions.loading = true;
    // const res ={}
    // pagerConfig.total = res.total;
    // gridOptions.data = res.list;
  } finally {
    gridOptions.loading = false;
  }
}

function close() {
  return props.modalConfirm();
}

onMounted(() => {
  handlePageData();
});
</script>
