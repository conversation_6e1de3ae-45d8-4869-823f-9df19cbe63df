<template>
  <div>
    <PageHeader v-if="submitStreamID" :submitStreamID="submitStreamID" @back="goBack">
      <template #extra>
        <div class="flex items-center gap-20px">
          <Popconfirm
            title="确认操作？"
            okText="确认"
            cancelText="取消"
          >
            <Button shape="round">
              <div class="flex items-center">
                <span>全部更新</span>
              </div>
            </Button>
          </Popconfirm>
          <Popconfirm
            title="确认操作？"
            okText="确认"
            cancelText="取消"
          >
            <Button shape="round">
              <div class="flex items-center">
                <span>全部重启</span>
              </div>
            </Button>
          </Popconfirm>
          <Button shape="round" @click="handleEditRule">
            <div class="flex items-center">
              <span>实例分配规则</span>
            </div>
          </Button>
          <Button shape="round" @click="handleShowOperation">
            <div class="flex items-center">
              <span>操作记录</span>
            </div>
          </Button>
        </div>
      </template>
    </PageHeader>
    <div class="example-config-list">
      <div v-for="item in 3" :key="item" class="example-config-list-item mx-20px my-10px flex justify-between b-rd-8px p-20px bg-FO-Container-Fill1!">
        <div class="flex items-center">
          <DragOutlined class="list-drag-btn hidden h-10px w-10px cursor-grab" />
          <div class="list-status-dot h-10px w-10px b-rd-5px" :class="{ 'bg-FO-Datavis-Yellow2': item === ExampleStatus.Busy, 'bg-FO-Datavis-Blue2': item === ExampleStatus.Idle, 'bg-FO-Content-Text4': item === ExampleStatus.Offline, 'bg-FO-Functional-Error1-Default': item === ExampleStatus.Disabled }" />
          <div class="ml-10px">
            <div class="flex items-center gap-10px">
              <span class="FO-Font-B16">实例1</span><Icon :icon="editIcon" class="cursor-pointer" @click="editExampleDetail" />
            </div>
            <div class="flex gap-20px c-FO-Content-Text3">
              <span>类型：非编译</span>
              <span>Workspace:asdf</span>
              <span>IP:127.0.0.1:3000</span>
            </div>
          </div>
        </div>

        <div class="flex items-center gap-10px">
          <div class="flex gap-10px">
            <span>实例以禁用</span>
            <span>更新中</span>
          </div>
          <div class="flex gap-10px">
            <Button>更新</Button>
            <Button>重启</Button>
            <Button>禁用</Button>
          </div>
        </div>
      </div>
    </div>
    <AssignRuleModalHolder />
    <ExampleDetailDrawerHolder />
    <OperationDrawerHolder />
  </div>
</template>

<script setup lang="ts">
import { DragOutlined } from '@ant-design/icons-vue';
import PageHeader from '../../PageHeader.vue';
import { useRouter } from 'vue-router';
import { Button, Popconfirm } from 'ant-design-vue';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import { nextTick, onMounted } from 'vue';
import { useSortable } from '../../../hooks/useSortable';
import { ExampleStatus } from '../../commit-center-home/steams.data';
import editIcon from '@iconify-icons/icon-park-outline/edit';
import { Icon } from '@iconify/vue';
import AssignRuleModal from './AssignRuleModal.vue';
import { useModalShow } from '@hg-tech/utils-vue';
import ExampleDetailDrawer from './ExampleDetailDrawer.vue';
import OperationDrawer from './OperationDrawer.vue';

const [AssignRuleModalHolder, showAssignRuleModal] = useModalShow(AssignRuleModal);
const [ExampleDetailDrawerHolder, showExampleDetailDrawer] = useModalShow(ExampleDetailDrawer);
const [OperationDrawerHolder, showOperationRecordModal] = useModalShow(OperationDrawer);
const router = useRouter();
const routeParams = router.currentRoute.value.params;
const submitStreamID = routeParams.submitStreamID ? Number(routeParams.submitStreamID) : null;
function handleEditRule() {
  showAssignRuleModal({});
}
function handleShowOperation() {
  showOperationRecordModal({});
}
function goBack() {
  router.push({
    name: PlatformEnterPoint.Example,
    params: {
      submitStreamID,
    },
  });
}

function editExampleDetail() {
  showExampleDetailDrawer({});
}
// 初始化拖拽
function initDrag() {
  nextTick(() => {
    const el = document.querySelector(`.example-config-list`) as HTMLElement;
    useSortable(el, {
      handle: `.list-drag-btn`,
      onEnd: async ({ oldIndex, newIndex }: { oldIndex?: number; newIndex?: number }) => {
        console.log('🚀 ~ initDrag ~ oldIndex, newIndex:', oldIndex, newIndex);
      },
    });
  });
}
onMounted(() => {
  initDrag();
});
</script>

<style lang="less" scoped>
.example-config-list-item {
  &:hover {
    .list-drag-btn {
      display: block;
    }
    .list-status-dot {
      display: none;
    }
  }
}
</style>
