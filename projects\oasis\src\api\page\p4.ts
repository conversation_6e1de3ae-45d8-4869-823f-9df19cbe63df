import type { BasicAddResult, NullableBasicResult } from '../model/baseModel';
import type { TagListItem } from './model/submitConfModel';
import type { SuccessMessageMode } from '/#/axios';
import type {
  BatchP4GroupPriorityParams,
  CommitParamsGetResultModel,
  CommitParamsItem,
  CommitParamsWorkspaceFieldsGetResultModel,
  CommitTransitionGetResultModel,
  CopyP4StreamParams,
  DepotsItemParams,
  DepotsListGetResultModel,
  DepotsListItem,
  DepotsPageParams,
  DMO1GroupItemParams,
  GetP4TreeCurNodeListGetResultModel,
  GetP4TreeCurNodeParams,
  IssueItem,
  KeyAttributesGetResultModel,
  KeyAttributesParams,
  LDAPGroupListGetResultModel,
  LDAPGroupPageParams,
  P4AutoMergeListItem,
  P4AutoMergePageParams,
  P4CompleteNoticeGetResultModel,
  P4CompleteNoticeListGetResultModel,
  P4CompleteNoticeListItem,
  P4ConcernGroupListItem,
  P4GroupListGetResultModel,
  P4GroupListItem,
  P4GroupMemberListGetResultModel,
  P4GroupPageParams,
  P4MemberListGetResultModel,
  P4MemberPageParams,
  P4PermissionListGetResultModel,
  P4PermissionPageParams,
  P4SpecialUserListGetResultModel,
  P4SpecialUserListItem,
  P4SpecialUserPageParams,
  P4SubmitCheckGetResultModel,
  P4WhiteListItem,
  RecordStreamOperationParams,
  StateNodesGetResultModel,
  StoryItem,
  streamGroupInfoItem,
  StreamOperationGetResultModel,
  StreamsItemParams,
  StreamsListGetResultModel,
  StreamsListItem,
  StreamsPageParams,
  TagUsedStreamsResultModel,
  UpdateP4ConcernGroupMemberListParams,
  UpdateP4PermissionListParams,
  UserDM01GroupListParams,
  ValidateDM01StreamExistParams,
  ValidateDM01StreamExistResultModel,
  VersionsItemParams,
  VersionsListGetResultModel,
  VersionsListItem,
  VersionsPageParams,
  WhiteListGetResultModel,
} from '/@/api/page/model/p4Model';
import { defHttp } from '/@/utils/http/axios';

enum Api {
  Projects = '/api/v1/projects',
  DM01 = '/api/v1/dm01',
  Depots = '/depots',
  Streams = '/streams',
  Versions = '/stream/versions',
  DM01Group = '/stream/groups',
  P4Groups = '/p4_groups',
  Members = '/members',
  Transfer = '/transfer',
  Permissions = '/permissions',
  Hierarchy = '/hierarchy',
  Batch = '/batch',
  P4Users = '/p4_users',
  RestrictUsers = '/restrict_users',
  Sync = '/sync',
  LoadHierarchy = '/loadHierarchy',
  P4Servers = '/api/v1/perforce/servers',
}

/**
 * 获取仓库列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getDepotsListByPage(projectID: number, params?: DepotsPageParams) {
  return defHttp.get<DepotsListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.Depots}`,
    params,
  });
}

/**
 * 根据id获取仓库信息
 * @param projectID 项目id
 * @param ID 仓库id
 */
export function getDepotByID(projectID: number, ID: string) {
  return defHttp.get<DepotsItemParams>({ url: `${Api.Projects}/${projectID}${Api.Depots}/${ID}` });
}

/**
 * 新增仓库
 * @param projectID 项目id
 * @param data 仓库数据
 */
export function addDepot(projectID: number, data: DepotsListItem) {
  return defHttp.post<BasicAddResult>({ url: `${Api.Projects}/${projectID}${Api.Depots}`, data });
}

/**
 * 编辑仓库
 * @param projectID 项目id
 * @param data 仓库数据
 * @param editId 仓库id
 */
export function editDepot(projectID: number, data: DepotsListItem, editId: number) {
  return defHttp.put<null>({ url: `${Api.Projects}/${projectID}${Api.Depots}/${editId}`, data });
}

/**
 * 删除仓库
 * @param projectID 项目id
 * @param editId 仓库id
 */
export function deleteDepot(projectID: number, editId: number) {
  return defHttp.delete<null>({ url: `${Api.Projects}/${projectID}${Api.Depots}/${editId}` });
}

/**
 * 获取分支列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getStreamsListByPage(projectID: number, params?: StreamsPageParams) {
  return defHttp.get<StreamsListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.Streams}`,
    params,
  });
}

/**
 * 根据id获取分支信息
 * @param projectID 项目id
 * @param ID 分支id
 */
export function getStreamsByID(projectID: number, ID: number) {
  return defHttp.get<StreamsItemParams>({ url: `${Api.Projects}/${projectID}${Api.Streams}/${ID}` });
}

/**
 * 创建分支(P4创建)
 * @param projectID 项目id
 * @param data 分支数据
 */
export function createStream(projectID: number, data: StreamsListItem) {
  return defHttp.post<BasicAddResult>({ url: `${Api.Projects}/${projectID}${Api.Streams}/create`, data });
}

/**
 * 添加分支(P4存在的分支)
 * @param projectID 项目id
 * @param data 分支数据
 */
export function addStream(projectID: number, data: StreamsListItem) {
  return defHttp.post<BasicAddResult>({ url: `${Api.Projects}/${projectID}${Api.Streams}`, data });
}

/**
 * 编辑分支
 * @param projectID 项目id
 * @param data 分支数据
 * @param editId 分支id
 */
export function editStream(projectID: number, data: StreamsListItem, editId: number) {
  return defHttp.put<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}${Api.Streams}/${editId}`,
    data,
  });
}

/**
 * 删除分支
 * @param projectID 项目id
 * @param editId 分支id
 */
export function deleteStream(projectID: number, editId: number) {
  return defHttp.delete<null>({ url: `${Api.Projects}/${projectID}${Api.Streams}/${editId}` });
}

/**
 * 获取P4干员组列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getP4GroupListByPage(projectID: number, params?: P4GroupPageParams) {
  return defHttp.get<P4GroupListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.P4Groups}`,
    params,
  });
}

/**
 * 新增P4干员组
 * @param projectID 项目id
 * @param data P4干员组数据
 */
export function addP4Group(projectID: number, data: P4GroupListItem) {
  return defHttp.post<BasicAddResult>({ url: `${Api.Projects}/${projectID}${Api.P4Groups}`, data });
}

/**
 * 编辑P4干员组
 * @param projectID 项目id
 * @param data 项目数据
 * @param editId P4干员组id
 */
export function editP4Group(projectID: number, data: P4GroupListItem, editId: number) {
  return defHttp.put<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}${Api.P4Groups}/${editId}`,
    data,
  });
}

/**
 * 删除P4干员组
 * @param projectID 项目id
 * @param editId P4干员组id
 */
export function deleteP4Group(projectID: number, editId: number) {
  return defHttp.delete<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}${Api.P4Groups}/${editId}`,
  });
}

/**
 * 批量更新P4干员组权重
 * @param projectID 项目id
 * @param data 新干员组id列表
 */
export function batchP4GroupPriority(projectID: number, data: BatchP4GroupPriorityParams) {
  return defHttp.put<null>({ url: `${Api.Projects}/${projectID}${Api.P4Groups}/batch_priority`, data });
}

/**
 * 获取P4特殊干员列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getP4SpecialUserListByPage(projectID: number, params?: P4SpecialUserPageParams) {
  return defHttp.get<P4SpecialUserListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.P4Users}`,
    params,
  });
}

/**
 * 获取限制提交干员
 * @param projectID 项目id
 */
export function getLimitSmitUserListByProjectID(projectID: number) {
  return defHttp.get<P4SpecialUserListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.RestrictUsers}`,
  });
}

/**
 * 添加限制提交干员
 * @param projectID 项目id
 */
export function addLimitSmitUser(projectID: number, params: { idList: number[] }) {
  return defHttp.put<P4SpecialUserListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.RestrictUsers}`,
    params,
  });
}

/**
 * 添加限制提交干员
 * @param projectID 项目id
 */
export function deleteLimitSmitUser(projectID: number, params: { idList: number[] }) {
  return defHttp.delete<P4SpecialUserListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.RestrictUsers}`,
    params,
  });
}

/**
 * 新增P4特殊干员组
 * @param projectID 项目id
 * @param data P4特殊干员数据
 */
export function addP4SpecialUser(projectID: number, data: P4SpecialUserListItem) {
  return defHttp.post<NullableBasicResult>(
    { url: `${Api.Projects}/${projectID}${Api.P4Users}`, data },
    { errorMessageMode: 'none' },
  );
}

/**
 * 编辑P4特殊干员组
 * @param projectID 项目id
 * @param data 项目数据
 * @param editId P4特殊干员id
 */
export function editP4SpecialUser(projectID: number, data: P4SpecialUserListItem, editId: number) {
  return defHttp.put<null>({ url: `${Api.Projects}/${projectID}${Api.P4Users}/${editId}`, data });
}

/**
 * 删除P4特殊干员组
 * @param projectID 项目id
 * @param editId P4特殊干员id
 */
export function deleteP4SpecialUser(projectID: number, editId: number) {
  return defHttp.delete<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}${Api.P4Users}/${editId}`,
  });
}

/**
 * 获取P4干员组干员列表
 * @param projectID 项目id
 * @param groupID 干员组id
 * @param params 筛选条件
 */
export function getP4MemberListByPage(projectID: number, groupID: number, params?: P4MemberPageParams) {
  return defHttp.get<P4MemberListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.P4Groups}/${groupID}${Api.Members}`,
    params,
  });
}

/**
 * 获取P4权限树当前节点子节点数据
 * @param projectID 项目id
 * @param streamID 分支id
 * @param params 筛选条件
 */
export function getP4TreeCurNodeList(projectID: number, streamID: number, params?: GetP4TreeCurNodeParams) {
  return defHttp.get<GetP4TreeCurNodeListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.Streams}/${streamID}/list`,
    params,
  });
}

/**
 * 获取P4权限树数据
 * @param projectID 项目id
 * @param streamID 分支id
 * @param params 筛选条件
 */
export function getP4TreeDataByPage(projectID: number, streamID: string, params?: P4PermissionPageParams) {
  return defHttp.get<P4PermissionListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.Streams}/${streamID}${Api.Hierarchy}`,
    params,
  });
}

/**
 * 获取P4干员组权限列表
 * @param projectID 项目id
 * @param groupID 干员组id
 * @param params 筛选条件
 */
export function getP4GroupPermissionListByPage(projectID: number, groupID: number, params?: P4PermissionPageParams) {
  return defHttp.get<P4PermissionListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.P4Groups}/${groupID}${Api.Permissions}`,
    params,
  });
}

/**
 * 更新P4干员组权限列表
 * @param projectID 项目id
 * @param groupID 干员组id
 * @param params 筛选条件
 */
export function updateP4GroupPermissionList(projectID: number, groupID: number, params?: UpdateP4PermissionListParams) {
  return defHttp.post<P4PermissionListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.P4Groups}/${groupID}${Api.Permissions}${Api.Batch}`,
    params,
  });
}

/**
 * 获取P4特殊干员权限列表
 * @param projectID 项目id
 * @param userID 特殊干员id
 * @param params 筛选条件
 */
export function getP4SpecialUserPermissionListByPage(projectID: number, userID: number, params?: P4PermissionPageParams) {
  return defHttp.get<P4PermissionListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.P4Users}/${userID}${Api.Permissions}`,
    params,
  });
}

/**
 * 更新P4特殊干员权限列表
 * @param projectID 项目id
 * @param userID 特殊干员id
 * @param params 筛选条件
 */
export function updateP4SpecialUserPermissionList(projectID: number, userID: number, params?: UpdateP4PermissionListParams) {
  return defHttp.post<P4PermissionListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.P4Users}/${userID}${Api.Permissions}${Api.Batch}`,
    params,
  });
}

/**
 * 获取项目仓库LDAP干员组
 * @param projectID 项目id
 * @param depotID 仓库id
 * @param params
 */
export function getLDAPGroupListByPage(projectID: number, depotID: number, params?: LDAPGroupPageParams) {
  return defHttp.get<LDAPGroupListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.Depots}/${depotID}/ldap_groups`,
    params,
  });
}

/**
 * 同步项目仓库LDAP干员组干员到P4干员组
 * @param projectID 项目id
 * @param depotID 仓库id
 * @param groupID P4干员组id
 */
export function syncLDAPGroupListToP4Group(projectID: number, depotID: string, groupID: string) {
  return defHttp.post<null>({
    url: `${Api.Projects}/${projectID}${Api.Depots}/${depotID}/groups/${groupID}/sync_ldap_group`,
  });
}

/**
 * 复制项目仓库分支权限
 * @param projectID 项目id
 * @param depotID 仓库id
 * @param params 复制参数
 */
export function copyP4StreamPermission(projectID: number, depotID: number, params: CopyP4StreamParams) {
  return defHttp.post<NullableBasicResult>(
    {
      url: `${Api.Projects}/${projectID}${Api.Depots}/${depotID}${Api.Permissions}/copy`,
      params,
    },
    {
      successMessageMode: 'none',
      errorMessageMode: 'none',
    },
  );
}

/**
 * 更新分支组信息
 * @param projectID 项目id
 * @param streamID 分支id
 * @param data 组数据
 */
export function updateStreamGroupInfo(projectID: number, streamID: number, data: streamGroupInfoItem) {
  return defHttp.put<null>({ url: `${Api.Projects}/${projectID}${Api.Streams}/${streamID}/groups`, data });
}

/**
 * 开分支操作记录
 * @param projectID 项目id
 * @param streamID 分支id
 * @param params 筛选条件
 */
export function recordStreamOperation(projectID: number, streamID: number, params?: RecordStreamOperationParams) {
  return defHttp.post<null>(
    {
      url: `${Api.Projects}/${projectID}${Api.Streams}/${streamID}/operation`,
      params,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 获取开分支操作记录
 * @param projectID 项目id
 * @param streamID 分支id
 */
export function getStreamOperationRecord(projectID: number, streamID: number) {
  return defHttp.get<StreamOperationGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.Streams}/${streamID}/operation`,
  });
}

/**
 * 查看P4自动合并详情
 * @param projectID 项目id
 * @param streamID 分支id
 * @param params 筛选条件
 */
export function getP4AutoMergeDetail(projectID: number, streamID: number, params?: P4AutoMergePageParams) {
  return defHttp.get<P4AutoMergeListItem>({
    url: `${Api.Projects}/${projectID}${Api.Streams}/${streamID}/merge`,
    params,
  });
}

/**
 * 新增P4自动合并组
 * @param projectID 项目id
 * @param streamID 分支id
 * @param data P4自动合并数据
 */
export function addP4AutoMerge(projectID: number, streamID: number, data: P4AutoMergeListItem[]) {
  return defHttp.post<null>({ url: `${Api.Projects}/${projectID}${Api.Streams}/${streamID}/merge`, data });
}

/**
 * 编辑P4自动合并组
 * @param projectID 项目id
 * @param streamID 分支id
 * @param data 项目数据
 */
export function editP4AutoMerge(projectID: number, streamID: number, data: P4AutoMergeListItem[]) {
  return defHttp.put<null>({
    url: `${Api.Projects}/${projectID}${Api.Streams}/${streamID}/merge`,
    data,
  });
}

/**
 * 删除P4自动合并组
 * @param projectID 项目id
 * @param streamID 分支id
 */
export function deleteP4AutoMerge(projectID: number, streamID: number) {
  return defHttp.delete<null>({
    url: `${Api.Projects}/${projectID}${Api.Streams}/${streamID}/merge`,
  });
}

/**
 * 获取项目仓库提交检查信息
 * @param projectID 项目id
 * @param depotID 仓库id
 */
export function getDepotCheckInfo(projectID: number, depotID: number) {
  return defHttp.get<P4SubmitCheckGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.Depots}/${depotID}/check`,
  });
}

/**
 * 获取P4关注组列表
 * @param projectID 项目id
 * @param params 筛选条件
 */

/**
 * 获取DM01分支列表
 * @param params 筛选条件
 */
export function getDM01StreamsListByPage(params?: StreamsPageParams) {
  return defHttp.get<StreamsListGetResultModel>({
    url: `${Api.DM01}${Api.Streams}`,
    params,
  });
}

/**
 * 根据id获取DM01分支信息
 * @param ID 分支id
 */
export function getDM01StreamsByID(ID: number) {
  return defHttp.get<StreamsItemParams>({ url: `${Api.DM01}${Api.Streams}/${ID}` });
}

/**
 * 创建DM01分支(P4创建)
 * @param data 分支数据
 */
export function createDM01Stream(data: StreamsListItem) {
  return defHttp.post<BasicAddResult>({ url: `${Api.DM01}${Api.Streams}/create`, data });
}

/**
 * 添加DM01分支(P4存在的分支)
 * @param data 分支数据
 */
export function addDM01Stream(data: StreamsListItem) {
  return defHttp.post<BasicAddResult>({ url: `${Api.DM01}${Api.Streams}`, data });
}

/**
 * 编辑DM01分支
 * @param data 分支数据
 * @param editId 分支id
 */
export function editDM01Stream(data: StreamsListItem, editId: number) {
  return defHttp.put<NullableBasicResult>({
    url: `${Api.DM01}${Api.Streams}/${editId}`,
    data,
  });
}

/**
 * 删除DM01分支
 * @param editId 分支id
 */
export function deleteDM01Stream(editId: number) {
  return defHttp.delete<null>({ url: `${Api.DM01}${Api.Streams}/${editId}` });
}

/**
 * 获取DM01分支版本列表
 * @param params 筛选条件
 */
export function getDM01VersionsListByPage(params?: VersionsPageParams) {
  return defHttp.get<VersionsListGetResultModel>({
    url: `${Api.DM01}${Api.Versions}`,
    params,
  });
}

/**
 * 根据id获取DM01分支版本
 * @param ID 分支id
 */
export function getDM01VersionsByID(ID: number) {
  return defHttp.get<VersionsItemParams>({ url: `${Api.DM01}${Api.Versions}/${ID}` });
}

/**
 * 添加DM01分支版本
 * @param data 分支数据
 */
export function addDM01Version(data: VersionsListItem, successMessageMode: SuccessMessageMode = 'message') {
  return defHttp.post<BasicAddResult>(
    { url: `${Api.DM01}${Api.Versions}`, data },
    {
      successMessageMode,
    },
  );
}

/**
 * 编辑DM01分支版本
 * @param data 分支数据
 * @param editId 分支id
 */
export function editDM01Version(data: VersionsListItem, editId: number) {
  return defHttp.put<NullableBasicResult>({
    url: `${Api.DM01}${Api.Versions}/${editId}`,
    data,
  });
}

/**
 * 验证DM01分支是否存在
 * @param params 参数
 */
export function validateDM01StreamExist(params?: ValidateDM01StreamExistParams) {
  return defHttp.post<ValidateDM01StreamExistResultModel>(
    {
      url: `${Api.DM01}${Api.Streams}/exist`,
      params,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 获取DM01分组列表
 * @param params 筛选条件
 */
export function getDM01GroupListByPage(params?: P4GroupPageParams) {
  return defHttp.get<P4GroupListGetResultModel>({
    url: `${Api.DM01}${Api.DM01Group}`,
    params,
  });
}

/**
 * 获取DM01分组列表
 * @param groupID 关注组id
 * @param params 筛选条件
 */
export function getDM01GroupInfoByID(groupID: number, params?: P4GroupListItem) {
  return defHttp.get<DMO1GroupItemParams>({
    url: `${Api.DM01}${Api.DM01Group}/${groupID}`,
    params,
  });
}

/**
 * 同步P4组成员到DM01组
 * @param groupID 关注组id
 */
export function syncDM01GroupMemberList(groupID: number) {
  return defHttp.post<null>(
    {
      url: `${Api.DM01}${Api.DM01Group}/${groupID}/sync_ldap_group`,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 创建默认DM01组
 * @param data 项目数据
 */
export function createDefaultDM01Group(data: P4ConcernGroupListItem) {
  return defHttp.post<null>(
    { url: `${Api.DM01}${Api.DM01Group}/default`, data },
    { successMessageMode: 'none' },
  );
}

/**
 * 编辑DM01组
 * @param data 项目数据
 * @param editId DM01组id
 */
export function editDM01Group(data: P4ConcernGroupListItem, editId: number) {
  return defHttp.put<null>({ url: `${Api.DM01}${Api.DM01Group}/${editId}`, data });
}

/**
 * 更新DM01组干员列表
 * @param groupID DM01组id
 * @param params 筛选条件
 */
export function updateDM01GroupMemberList(groupID: number, params?: UpdateP4ConcernGroupMemberListParams) {
  return defHttp.put<NullableBasicResult>(
    {
      url: `${Api.DM01}${Api.DM01Group}/${groupID}/members`,
      params,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 获取DM01仓库LDAP干员组
 * @param groupID 组id
 * @param params
 */
export function getDM01LDAPGroupListByPage(groupID: number, params?: P4GroupPageParams) {
  return defHttp.get<LDAPGroupListGetResultModel>({
    url: `${Api.DM01}${Api.DM01Group}/${groupID}/sync_ldap_group`,
    params,
  });
}

/**
 * 获取当前用户DM01分组列表
 * @param params 筛选条件
 */
export function getUserDM01GroupList(params?: P4ConcernGroupListItem) {
  return defHttp.get<UserDM01GroupListParams>({
    url: `${Api.DM01}${Api.DM01Group}/user`,
    params,
  });
}

/**
 * 获取指定P4用户组的成员列表
 * @param groupName P4组名
 * @param serverID 服务器ID
 */
export function getP4GroupMembers(groupName: string, serverID?: number) {
  return defHttp.get<P4GroupMemberListGetResultModel>({
    url: `${Api.P4Servers}/group/members`,
    params: {
      groupName,
      serverID,
    },
  });
}

/**
 * 配置白名单
 * @param projectID 项目id
 * @param data 白名单数据
 */
export function configWhiteList(projectID: number, data: P4WhiteListItem) {
  return defHttp.post<null>({ url: `${Api.Projects}/${projectID}/white_lists`, data });
}

/**
 * 更新白名单
 * @param projectID 项目id
 * @param whiteId 白名单id
 * @param data 白名单数据
 */
export function updateWhiteList(projectID: number, whiteId: number, data: P4WhiteListItem) {
  return defHttp.put<null>({ url: `${Api.Projects}/${projectID}/white_lists/${whiteId}`, data });
}

/**
 * 获取提交白名单列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getWhiteListByPage(projectID: number, params: P4PermissionPageParams) {
  return defHttp.get<WhiteListGetResultModel>({ url: `${Api.Projects}/${projectID}/white_lists`, params });
}

/**
 * 获取指定ID的白名单
 * @param projectID 项目id
 * @param whiteId 白名单id
 */
export function getWhiteListByID(projectID: number, whiteId: number) {
  return defHttp.get<null>({ url: `${Api.Projects}/${projectID}/white_lists/${whiteId}` });
}

/**
 * 配置可提交单号
 */
export function configCommitParams(projectID: number, streamID: number) {
  return defHttp.post<BasicAddResult>(
    {
      url: `/api/v1/projects/${projectID}/commit/workItem/config`,
      data: { streamID },
    },
  );
}

/**
 * 指定分支克隆可提交单号配置
 */
export function cloneCommitParams(projectID: number, data: { fromStreamID: number; toStreamID: number }) {
  return defHttp.post<BasicAddResult>(
    {
      url: `/api/v1/projects/${projectID}/commit/workItem/config/clone`,
      data,
    },
  );
}

/**
 * 删除可提交单号配置
 */
export function deleteCommitParams(projectID: number, streamID: number) {
  return defHttp.post<BasicAddResult>(
    {
      url: `/api/v1/projects/${projectID}/commit/workItem/config/delete`,
      data: { streamID },
    },
  );
}

/**
 * 获取单号配置开关
 */
export function getConfigInfo(projectID: number, streamID: number) {
  return defHttp.get<{
    id?: number;
    enable?: number;
    bindOneWorkItemIDLimit?: number;
  }>(
    {
      url: `/api/v1/projects/${projectID}/commit/workItem/config/info`,
      params: { streamID },
    },
  );
}

/**
 * 获取单号配置开关
 */
export function editConfigInfo(projectID: number, streamID: number, enable: 1 | 0, bindOneWorkItemIDLimit: 1 | 0) {
  return defHttp.post<BasicAddResult>(
    {
      url: `/api/v1/projects/${projectID}/commit/workItem/config/enableSwitch`,
      params: { streamID, enable, bindOneWorkItemIDLimit },
    },
    { successMessageMode: 'none' },
  );
}

/**
 * 获取可提交单号配置空间字段
 */
export function getCommitParamsWorkspaceFields(projectID: number, type: 'story' | 'issue') {
  return defHttp.get<CommitParamsWorkspaceFieldsGetResultModel>({
    url: `/api/v1/projects/${projectID}/commit/pms/meta/${type}`,
  });
}

/**
 * 获取可提交单号配置列表
 */
export function getCommitParamsValue(projectID: number, streamID: number) {
  return defHttp.get<CommitParamsGetResultModel>({
    url: `/api/v1/projects/${projectID}/commit/params`,
    params: {
      streamID,
      page: 1,
      pageSize: 10,
    },
    timeout: 60 * 1000,
  });
}

/**
 * 获取提交单号自动流转配置
 */
export function getCommitTransitionValue(projectID: number, streamID: number) {
  return defHttp.get<CommitTransitionGetResultModel>({
    url: `/api/v1/projects/${projectID}/commit/transition`,
    params: {
      streamID,
    },
  });
}

/**
 * 获取项目的可配置流转节点
 */
export function getStateNodes(projectID: number, workItemTypeKey: 'story' | 'issue') {
  return defHttp.get<StateNodesGetResultModel>({
    url: `/api/v1/projects/${projectID}/commit/pms/state_nodes/${workItemTypeKey}`,
  });
}
/**
 * 保存分支的提交流转配置
 */
export function setCommitTransitionValue(projectID: number, streamID: number, story: StoryItem[], issue: IssueItem[]) {
  return defHttp.post<BasicAddResult>({
    url: `/api/v1/projects/${projectID}/commit/transition/update`,
    params: {
      streamID,
      story,
      issue,
    },

  }, { successMessageMode: 'none' });
}

/**
 * 新增可提交单号配置
 */
export function addCommitParams(projectID: number, data: CommitParamsItem) {
  return defHttp.post<BasicAddResult>(
    {
      url: `${Api.Projects}/${projectID}/commit/params`,
      data,
      timeout: 60 * 1000,
    },
    { successMessageMode: 'none' },
  );
}

/**
 * 编辑可提交单号配置
 */
export function editCommitParams(projectID: number, data: CommitParamsItem, editId: number) {
  return defHttp.put<BasicAddResult>(
    {
      url: `/api/v1/projects/${projectID}/commit/params/${editId}`,
      data,
      timeout: 60 * 1000,
    },
    { successMessageMode: 'none' },
  );
}

/**
 * 复制通知规则
 * @param projectID 项目id
 * @param params 复制参数
 */
export function copyP4CompleteNotice(projectID: number, params: CopyP4StreamParams) {
  return defHttp.post<NullableBasicResult>(
    {
      url: `/api/v1/projects/${projectID}/commit_notice/rules/copy`,
      params,
    },
    { successMessageMode: 'none' },
  );
}

/**
 * 获取通知规则列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getP4CompleteNoticeListByPage(projectID: number, params: P4PermissionPageParams) {
  return defHttp.get<P4CompleteNoticeListGetResultModel>({
    url: `/api/v1/projects/${projectID}/commit_notice/rules`,
    params,
  });
}

/**
 * 获取指定ID的通知规则
 * @param projectID 项目id
 */
export function getP4CompleteNoticeByID(projectID: number, ID: number) {
  return defHttp.get<P4CompleteNoticeGetResultModel>({
    url: `/api/v1/projects/${projectID}/commit_notice/rules/${ID}`,
  });
}

/**
 * 删除分支的通知规则
 * @param projectID 项目id
 * @param streamID 分支id
 */
export function deleteCompleteNoticeStream(projectID: number, streamID: number) {
  return defHttp.delete<null>({
    url: `/api/v1/projects/${projectID}/commit_notice/rules`,
    params: { streamID },
  });
}

/**
 * 删除分支的通知规则
 * @param projectID 项目id
 * @param ruleID 规则id
 */
export function deleteCompleteNotice(projectID: number, ruleID: number) {
  return defHttp.delete<null>({
    url: `/api/v1/projects/${projectID}/commit_notice/rules/${ruleID}`,
  });
}

/**
 * 更新通知规则
 * @param projectID 项目id
 * @param ruleId 规则id
 * @param data 规则数据
 */
export function updateCompleteNoticeList(projectID: number, ruleId: number, data: P4CompleteNoticeListItem) {
  return defHttp.put<null>({ url: `/api/v1/projects/${projectID}/commit_notice/rules/${ruleId}`, data });
}

/**
 * 创建通知规则
 * @param projectID 项目id
 * @param data 规则数据
 */
export function addCompleteNotice(projectID: number, data: P4CompleteNoticeListItem) {
  return defHttp.post<BasicAddResult>({ url: `/api/v1/projects/${projectID}/commit_notice/rules`, data });
}

/**
 * 删除指定分支的提交权限配置
 * @param projectID 项目id
 * @param streamID 分支id
 */
export function deleteSubmitPermissionBranch(projectID: number, streamID: number) {
  return defHttp.delete<NullableBasicResult>({
    url: `/api/v1/projects/${projectID}/streams/${streamID}/permission`,
  });
}

/**
 * 新增分支提交检查配置
 * @param projectID 项目id
 * @param streamID 分支id
 */
export function addSubmitConfig(projectID: number, streamID: number) {
  return defHttp.post<BasicAddResult>({
    url: `/v2/projects/${projectID}/resc/confs/${streamID}`,
  });
}

/**
 * 删除分支提交检查配置
 * @param projectID 项目id
 * @param streamID 分支id
 */
export function deleteSubmitConfig(projectID: number, streamID: number) {
  return defHttp.delete<NullableBasicResult>({
    url: `/v2/projects/${projectID}/resc/confs/${streamID}`,
  });
}

/**
 * 克隆其他分支提交检查配置
 * @param projectID 项目id
 * @param data 克隆参数
 * @param data.srcStreamID 克隆参数
 * @param data.dstStreamID 克隆参数
 */
export function cloneSubmitConfig(projectID: number, data: { srcStreamID: number; dstStreamID: number }) {
  return defHttp.put<NullableBasicResult>({
    url: `/v2/projects/${projectID}/resc/confs/clone`,
    data,
  });
}

/**
 *  查询项目配置
 */
export function getProConfig(id: number, name: string) {
  return defHttp.get<any>({
    url: `/api/v1/projects/${id}/config/${name}`,
  });
}

/**
 *  变更项目配置
 */
export function setProConfig(id: number, params: any) {
  return defHttp.post<any>({
    url: `/api/v1/projects/${id}/config`,
    params,
  });
}

/**
 *  更新分支排序
 */
export function updateStreamsSort(projectID: number, params: { idList: number[]; depotID: number }) {
  return defHttp.put<NullableBasicResult>({
    url: `/api/v1/projects/${projectID}/streams/sort`,
    params,
  });
}

/**
 *  更新仓库排序
 */
export function updateDepotsSort(projectID: number, params: { idList: number[] }) {
  return defHttp.put<NullableBasicResult>({
    url: `/api/v1/projects/${projectID}/depots/sort`,
    params,
  });
}

/**
 * 指定分支克隆可提交tag配置
 */
export function cloneTagConfig(projectID: number, data: { fromStreamID: number; toStreamID: number }) {
  return defHttp.post<BasicAddResult>(
    {
      url: `/api/v1/projects/${projectID}/tags/stream/clone`,
      data,
    },
    {
      errorMessageMode: 'none',
      successMessageMode: 'none',
    },
  );
}

/**
 * 配置可提交tag
 */
export function configCommitTag(projectID: number, data: { streamID: number; bindTag: boolean; tagIDs: number[] }) {
  return defHttp.post<BasicAddResult>(
    {
      url: `/api/v1/projects/${projectID}/tags/stream`,
      data,
    },
  );
}

/**
 * 获取tag配置
 */
export function getTagConfigInfo(projectID: number, streamID: number) {
  return defHttp.get<{
    bindTag?: boolean;
    commit?: TagListItem[];
    other?: TagListItem[];
  }>(
    {
      url: `/api/v1/projects/${projectID}/tags/stream`,
      params: { streamID },
    },
  );
}

/**
 * 更新分支tag配置的开关
 */
export function updateTagConfigSwitch(projectID: number, streamID: number, bindTag: boolean) {
  return defHttp.put<NullableBasicResult>(
    {
      url: `/api/v1/projects/${projectID}/tags/stream/switch`,
      params: { streamID, bindTag },
    },
  );
}

/**
 * 更新可提交tag
 */
export function updateCommitTag(projectID: number, streamID: number, tagIDs: number[]) {
  return defHttp.put<NullableBasicResult>(
    {
      url: `/api/v1/projects/${projectID}/tags/stream/commitTag`,
      params: { tagIDs, streamID },
    },
  );
}

/**
 * 删除分支tag配置
 */
export function deleteConfigTag(projectID: number, streamID: number) {
  return defHttp.delete<NullableBasicResult>(
    {
      url: `/api/v1/projects/${projectID}/tags/stream`,
      params: { streamID },
    },
  );
}

/**
 * 获取tag用于的分支
 */
export function getTagUsedStreams(projectID: number, tagID: number) {
  return defHttp.get<TagUsedStreamsResultModel>(
    {
      url: `/api/v1/projects/${projectID}/tags/${tagID}/stream`,
    },
  );
}

/**
 * 获取分支关键参数
 */
export function getKeyAttributes(projectID: number, streamID: number) {
  return defHttp.get<KeyAttributesGetResultModel>(
    {
      url: `/api/v1/projects/${projectID}/commit/key_attributes`,
      params: { streamID },
    },
  );
}

/**
 * 配置分支关键参数
 */
export function setKeyAttributes(projectID: number, params: KeyAttributesParams) {
  return defHttp.post<KeyAttributesGetResultModel>(
    {
      url: `/api/v1/projects/${projectID}/commit/key_attributes`,
      params,
    },
  );
}
