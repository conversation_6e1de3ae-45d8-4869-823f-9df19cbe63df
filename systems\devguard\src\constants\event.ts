/**
 * 定义事件名称的枚举, 事件名称的格式为: 模块名_事件名
 * @see https://dataark.hypergryph.net/area/11/app/app_manager/event_manager?appId=7l6k0y2vw4rksevp6bibwuht
 */
enum TrackEventName {
  // TODO: 请替换为实际的事件名称
  SUBMIT_CENTER_SUBMIT_RECORD_FILTER_CLICK = 'submit_center_submit_record_filter_click',
}

/**
 * 自定义事件参数类型
 * @description TODO：正式使用前请替换为实际的事件参数类型
 */
interface TodoEventParams {
  in_depth_think: boolean;
  in_online_search: boolean;
  model_use: string;
}

/**
 * 定义泛型类型来获取事件的入参类型，默认无参数，也可手动添加默认参数类型
 * @description TODO：正式使用前请替换为实际的事件参数类型
 */
 type TrackEventParams<T extends TrackEventName> =
  T extends TrackEventName ? TodoEventParams :
    undefined;

export {
  TrackEventName,
};
export type { TrackEventParams };
