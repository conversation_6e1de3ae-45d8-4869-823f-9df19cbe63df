<template>
  <div>
    <PageHeader v-if="submitStreamID" class="m-20px b-rd-8px bg-FO-Container-Fill1!" :submitStreamID="submitStreamID" @back="goBack()" />
    <div class="relative m-20px b-rd-8px bg-FO-Container-Fill1!">
      <div class="flex gap-30px p-20px">
        <div
          v-for="item in processTag" :key="item.name" class="cursor-pointer b-1 b-transparent b-rd-15px px-8px py-3px"
          :class="{ 'b-FO-Container-Stroke4!': item.name === nowTabName }" @click="tabChange(item.name)"
        >
          {{ item.label }}
        </div>
        <Input v-model:value="search" placeholder="搜索cl或提交人" class="w-300px b-rd-20px" @change="searchChange">
          <template #prefix>
            <SearchOutlined />
          </template>
        </Input>
      </div>
      <div style="height: calc(100vh - 270px)" class="overflow-auto px-20px pb-80px">
        <List :data-source="list" :split="false">
          <template #renderItem="{ item }">
            <div class="mb-10px b-rd-8px bg-FO-Container-Fill3">
              <ListItem>
                <div class="flex flex-1 flex-col">
                  <div class="flex items-center">
                    <div class="FO-Font-B16">
                      本地cl:{{ item.resCheckState === 3 ? item.submitCL : item.shelveCL }}
                    </div>
                    <Tooltip placement="bottom" @openChange="(value) => getSubmitInfo(item.ID, value)">
                      <template #title>
                        <div>提交人:{{ promptText?.submitterName }}</div>
                        <div>日期:{{ promptText?.date }}</div>
                        <div>描述:{{ promptText?.description }}</div>
                      </template>
                      <IIcon class="ml-5px" />
                    </Tooltip>
                    <div v-if="item.resCheckState === 3" class="ml-20px c-FO-Content-Text2">
                      本地cl：{{ item.shelveCL }}
                    </div>
                    <div class="ml-20px c-FO-Content-Text2">
                      提交人：{{ item.submitter.nickName }}
                    </div>
                  </div>
                  <div>
                    <Process :item="item" />
                  </div>
                </div>
                <div class="flex gap-10px">
                  <Button v-if="item.checkState === 2" size="small" class="px-10px!" @click="approval(item.ID)">
                    执行审批
                  </Button>
                  <div v-if="item.checkState === 2" class="h-20px w-1px bg-FO-Container-Stroke2" />
                  <Button v-if="[2, 3, 4].includes(item.reviewState)" size="small" class="px-10px!" @click="jumpPage(`${item.swarmBaseURL}/reviews/${item.reviewCL}`)">
                    <div class="flex items-center gap-2px">
                      <span>查看审查</span>
                      <Icon :icon="RightUpIcon" />
                    </div>
                  </Button>
                  <Button size="small" class="px-10px!" @click="jumpPage(`${item.swarmBaseURL}/changes/${item.shelveCL}`)">
                    <div class="flex items-center gap-2px">
                      <span>查看提交</span>
                      <Icon :icon="RightUpIcon" />
                    </div>
                  </Button>
                </div>
              </ListItem>
            </div>
          </template>
        </List>
      </div>
      <div class="absolute bottom-0 right-0 w-100% flex justify-end b-rd-8px bg-FO-Container-Fill1 p-20px">
        <Pagination v-model:current="pagination.currentPage" v-model:pageSize="pagination.pageSize" :total="pagination.total" size="small" :hideOnSinglePage="true" @change="getRecordList" />
      </div>
    </div>
    <ApprovalModalHolder />
  </div>
</template>

<script setup lang="ts">
import RightUpIcon from '@iconify-icons/icon-park-outline/arrow-right-up';

import { Icon } from '@iconify/vue';
import PageHeader from '../PageHeader.vue';
import { Button, Input, List, ListItem, Pagination, Tooltip } from 'ant-design-vue';
import { SearchOutlined } from '@ant-design/icons-vue';
import IIcon from '../../assets/icons/iIcon.svg?component';
import ApprovalModal from './ApprovalModal.vue';
import { useModalShow } from '@hg-tech/utils-vue';
import Process from './Process.vue';
import { onMounted, reactive, ref, watch } from 'vue';
import { type RecordListItem, type StreamsListItem, type SubmitInfo, applyQaCheckApi, getQaCheckInfoApi, getRecordListByPage, getStreamsInfo, getSubmitInfoApi } from '../../api';
import { debounce } from 'lodash';
import { useRouter } from 'vue-router';
import { useForgeonConfigStore } from '../../store/modules/forgeonConfig';
import { store } from '../../store/pinia';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import { processTag, processTagKey } from './type.data';
import { traceClickEvent } from 'src/services/track';
import { TrackEventName } from 'src/constants/event';

const router = useRouter();
const routeParams = router.currentRoute.value.params;
const submitStreamID = routeParams.submitStreamID ? Number(routeParams.submitStreamID) : null;

const [ApprovalModalHolder, showApprovalModal] = useModalShow(ApprovalModal);
const nowTabName = ref<processTagKey>(processTagKey.All);
const search = ref('');
const pagination = reactive({
  pageSize: 30,
  total: 0,
  currentPage: 1,
});
const streamsInfo = ref<StreamsListItem>();
const forgeonConfig = useForgeonConfigStore(store);

const list = ref<RecordListItem[]>([]);
const promptText = ref<SubmitInfo>();

async function getSubmitInfo(recordID: number, value: boolean) {
  if (value) {
    const res = await getSubmitInfoApi({ id: forgeonConfig.currentProjectId!, recordID }, {});
    promptText.value = res.data?.data;
  }
}

async function approval(ID: number) {
  const res = await getQaCheckInfoApi({ id: forgeonConfig.currentProjectId!, recordID: ID }, {});
  await showApprovalModal({
    checkQaList: res.data?.data?.checkQaList,
    shelveCL: res.data?.data?.shelveCL,
    submitter: res.data?.data?.submitter,
    checkTime: res.data?.data?.checkTime,
    description: res.data?.data?.description,
    canApply: res.data?.data?.canApply,
    workItemURL: res.data?.data?.workItemURL,
    workItemTitle: res.data?.data?.workItemTitle,
    sentReq: async (ApproveCode: number) => {
      const res = await applyQaCheckApi({ id: forgeonConfig.currentProjectId! }, { clRecordID: ID, ApproveCode });
      return res.data?.data;
    },
  });
  getRecordList();
}

function jumpPage(url: string) {
  window.open(url);
}

function tabChange(name: processTagKey) {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_SUBMIT_RECORD_FILTER_CLICK, {});
  nowTabName.value = name;
  pagination.currentPage = 1;
  pagination.pageSize = 30;
  getRecordList();
}

function goBack() {
  router.push({
    name: PlatformEnterPoint.CommitCenter,
  });
}

const searchChange = debounce(() => {
  pagination.currentPage = 1;
  pagination.pageSize = 30;
  getRecordList();
}, 300);

onMounted(async () => {
  if (!submitStreamID) {
    return;
  }
  const res = await getStreamsInfo({ id: forgeonConfig.currentProjectId!, stream_id: submitStreamID }, {});
  streamsInfo.value = res.data?.data?.stream;
  getRecordList();
});

async function getRecordList() {
  if (!submitStreamID) {
    return;
  }
  const res = await getRecordListByPage({
    id: forgeonConfig.currentProjectId!,
    streamID: submitStreamID,
    submitState: nowTabName.value,
    keyword: search.value,
    page: pagination.currentPage,
    pageSize: pagination.pageSize,
  }, {});
  list.value = res.data?.data?.list || [];
  pagination.total = res.data?.data?.total || 0;
}
watch(() => forgeonConfig.currentProjectId, () => {
  goBack();
});
</script>
