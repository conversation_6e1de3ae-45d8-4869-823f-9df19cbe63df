import type { BaseItem, BasicFetchResult, BasicPageParams, BasicResult } from '/@/api/model/baseModel';
import type { UserInfoModel } from '/@/api/sys/model/userModel';

// 仓库列表 Model
export interface DepotsListItem extends BaseItem {
  prefix?: string;
  depth?: number;
  isLocal?: boolean;
  serverID?: number;
  name?: string;
}
export enum importTypeMenu {
  includeImport = 1,
  fromImport = 2,
  point = 3,
}
// id获取仓库信息 接口参数
export interface DepotsItemParams {
  redepot: DepotsListItem;
}

// 仓库列表 接口参数
export type DepotsPageParams = BasicPageParams & DepotsListItem;

// 仓库列表 接口返回数据
export type DepotsListGetResultModel = BasicFetchResult<DepotsListItem>;

// 分支列表 Model
export interface StreamsListItem extends BaseItem {
  depotID?: number;
  path?: string;
  // 1：mainline，2：development，3：release，4：virtual，5：task
  streamType?: number;
  // 根目录名称
  rootDirectory?: string;
  // task用
  workspace?: string;
  groupList?: streamGroupInfoItem[];
  parent?: string;
  name?: string;
  autoMerge?: P4AutoMergeListItem[];
  step?: number;
  // 0关闭 1开启中 2部分成功 3全部成功 4全部失败
  checkStatus?: number;

  // 0关闭 1未锁定未配置 2未锁定已配置 3已锁定
  lockStatus?: number;
  versionID?: number;
  category?: number;
  user?: UserInfoModel;
  hasRescRpt?: boolean;
  permissionStatus?: boolean;
  hasClLabelRecord?: boolean;
  /**
   * 是否配置了提交单号
   */
  workItemConfig?: boolean;
  hasNoticeRule?: boolean;
  diffManageEnabled?: boolean;
  commitCheckEnabled?: boolean;
  tagConfig?: boolean;
  description?: string;
  filterProject?: boolean;
  permissionOrTag?: boolean;
}

// 仓库-分支下拉列表 Model
export interface streamOption {
  ID?: number;
  description?: string;
  options: StreamsListItem[];
}

// id获取分支信息 接口参数
export interface StreamsItemParams {
  restream: StreamsListItem;
}

// 分支列表 接口参数
export type StreamsPageParams = BasicPageParams & StreamsListItem;

// 分支列表 接口返回数据
export type StreamsListGetResultModel = BasicFetchResult<StreamsListItem>;
export interface TagUsedStreamsResultModel {
  streams: StreamsListItem[];
}
export interface KeyAttributesItem {
  story?: {
    streamID?: number;
    attributes?: {
      fieldName: string;
      fieldKey: string;
    }[];
  };
  issue?: {
    streamID?: number;
    attributes?: {
      fieldName: string;
      fieldKey: string;
    }[];
  };
}
export interface KeyAttributesParams {
  streamID?: number;
  workItemTypeKey: 'story' | 'issue';
  attributes: {
    fieldName: string;
    fieldKey: string;
  }[];
}
export interface KeyAttributesGetResultModel {
  list: KeyAttributesItem;

}

// P4干员组LDAP同步规则 Model
export interface P4GroupLdapRuleItem {
  operator?: 'or' | 'and';
  value?: string;
  children?: P4GroupLdapRuleItem[];
}

// P4干员组列表 Model
export interface P4GroupListItem {
  ID?: number;
  name?: string;
  description?: string;
  members?: P4MemberListItem[];
  projectID?: number;
  priority?: number;
  ldapRule?: P4GroupLdapRuleItem;
  syncP4Group?: boolean;
  userCount?: number;
  enable?: boolean;
  category?: number;
  depotID?: number;
  ldapGroups?: string[] | [];
  ignoreMember?: boolean;
  memberIds?: number[];
  serverID?: number;
  // 仅用于前端
  UUID?: number;
}

// 批量更新P4干员组权重接口参数 Model
export interface BatchP4GroupPriorityParams {
  idList: number[];
}

// P4干员组列表 接口参数
export type P4GroupPageParams = BasicPageParams & P4GroupListItem;

// P4干员组列表 接口返回数据
export type P4GroupListGetResultModel = BasicFetchResult<P4GroupListItem>;

// P4特殊干员列表 Model
export interface P4SpecialUserListItem {
  ID?: number;
  name?: string;
  description?: string;
  projectID?: number;
  sysUserId?: number;
  user?: P4MemberListItem;
  enable?: boolean;
  finishP4Orientation?: boolean;
  // 仅用于前端
  UUID?: number;
}

// P4特殊干员列表 接口参数
export type P4SpecialUserPageParams = BasicPageParams & P4SpecialUserListItem;

// P4特殊干员列表 接口返回数据
export type P4SpecialUserListGetResultModel = BasicFetchResult<P4SpecialUserListItem>;

// P4干员列表 Model
export interface P4MemberListItem extends UserInfoModel {
  sysUserId?: string;
  sysUser?: UserInfoModel;
  source?: number;
}

// 变更干员组干员 接口参数
export interface ChangeP4MemberParams {
  memberIds: number[];
}

// 转移干员组干员 接口参数
export type TransferP4MemberParams = {
  newGroupId: number;
} & ChangeP4MemberParams;

// P4干员列表 接口参数
export type P4MemberPageParams = BasicPageParams & P4MemberListItem;

// P4干员列表 接口返回数据
export type P4MemberListGetResultModel = BasicFetchResult<P4MemberListItem>;

// P4权限列表 Model
export interface P4PermissionListItem {
  ID?: string;
  name?: string;
  showName?: string;
  path?: string;
  type?: number;
  children?: P4PermissionListItem[];
  disabled?: boolean;
  isLeaf?: boolean;
  checked?: boolean;
  halfChecked?: boolean;
  authID?: string;
  permit?: number;
  reviewGroupID?: number;
  isLoaded?: boolean;
  hasChange?: boolean;

  // 仅用于前端
  childTypeList?: number[];
  parentPermit?: number;
  parentPath?: string;
  popVisible?: boolean;
  childPopVisible?: boolean;
  isRoot?: boolean;
}

// 获取P4权限树当前树节点列表 接口参数
export interface GetP4TreeCurNodeParams {
  currentPath?: string;
}

// 获取P4权限树当前树节点列表 model
export interface P4TreeCurNodeItem {
  kind: string;
  name: string;
  path: string;
  isLeaf?: boolean;
  checked?: boolean;
  halfChecked?: boolean;
  permit?: number;
  importType?: importTypeMenu;
  // 仅用于前端
  childTypeList?: number[];
}
// 获取P4权限树当前树节点列表 接口返回数据
export interface GetP4TreeCurNodeListGetResultModel {
  items: P4TreeCurNodeItem[];
}
// P4权限列表 接口参数
export type P4PermissionPageParams = {
  streamID?: number;
} & BasicPageParams &
P4PermissionListItem;

// 更新P4权限列表 model
export interface UpdateP4PermissionItem {
  path?: string;
  permit?: number;
}

export interface RegexCustomPath {
  regex?: string;
  validate?: string;
}
// 更新P4权限列表  接口参数
export interface UpdateP4PermissionListParams {
  streamID?: number;
  list: UpdateP4PermissionItem[];
}

// P4权限列表 接口返回数据
export type P4PermissionListGetResultModel = BasicFetchResult<P4PermissionListItem>;

// 项目仓库LDAP干员组列表 Model
export interface LDAPGroupListItem {
  ID?: number;
  name?: string;
  serverID?: number;
  disabled?: boolean;
  subGroupList?: LDAPGroupListItem[];
  subGroups?: string[];
  users?: UserInfoModel[];
}

// 项目仓库LDAP干员组列表 接口参数
export type LDAPGroupPageParams = BasicPageParams;

// 项目仓库LDAP干员组列表 接口返回数据
export type LDAPGroupListGetResultModel = BasicFetchResult<LDAPGroupListItem>;

// 项目仓库LDAP干员列表 Model
export interface LDAPMemberSubGroupListGetResultModel {
  group: LDAPGroupListItem;
}

// 复制P4分支权限  接口参数
export interface CopyP4StreamParams {
  fromStreamID: number;
  toStreamID: number;
}

// 分支组信息 Model
export interface streamGroupInfoItem extends BaseItem {
  groupID: number;
  enable: boolean;
  streamID?: number;
}

// 记录开分支操作 接口参数
export interface RecordStreamOperationParams {
  // 当前操作步骤数
  step?: number;
  // 当前操作步骤名：'CreateBranch'|'AddBranch', 'ConfigSubmitCheck', 'ClonePermission', 'CloneSwarm', 'ConfigAutoMerge', 'Finish'
  operation?: string;
  // 状态：0跳过，1成功，2失败
  status?: number;
}

export interface StreamOperationListItem extends BaseItem {
  projectID: number;
  streamID: number;
  userUUID: string;
  step: number;
  operation: string;
  status: number;
}
export type StreamOperationGetResultModel = BasicFetchResult<StreamOperationListItem>;

// P4自动合并列表 Model
export interface P4AutoMergeListItem {
  toStreamID?: number;
  // 目标分支路径
  toStream?: string;
  fromStream?: string;
  fromStreamID?: number;
  tag?: string;
  tagID?: number;
  webhook?: string;
  rule?: string;
  triggerID?: number;
}

// P4自动合并列表 接口参数
export type P4AutoMergePageParams = BasicPageParams & P4AutoMergeListItem;

export interface P4SubmitCheckItem extends BaseItem {
  projectName: string;
  depotID: number;
  p4User: string;
  p4Password: string;
  ip: string;
  loginUser: string;
  loginPassword: string;
  // 是否定制引擎
  customEngine: boolean;
  // 引擎路径
  enginePath: string;
  // 先用默认值
  etcdServer: string;
  etcdPort: number;
  checkerNumber: number;
  cacheServer: string;
  timestamp: number;
  loginTimestamp: number;
}

export interface P4SubmitCheckGetResultModel {
  check: P4SubmitCheckItem;
  id: number;
}

// P4引擎列表 Model
export interface P4EngineListItem extends BaseItem {
  version: string;
  path: string;
  projectID: number;
}

// P4引擎列表 接口返回数据
export type P4EngineGetResultModel = BasicFetchResult<P4EngineListItem>;

export interface OpenStreamCheckParams {
  checkID: number;
}

// 提交检查状态详情 Model
export interface SubmitCheckStateItem extends BaseItem {
  currentStep: number;
  errorInfo: string;
  stepName: string;
  stepNames: string[];
  totalStep: number;
  projectID: number;
  streamID: number;
}
export interface SubmitCheckStateGetResultModel {
  check: SubmitCheckStateItem;
}

export interface SubmitCheckStateResultItem extends BaseItem {
  projectID: number;
  streamID: number;
  checkerName: string;
  streamPath: string;
  logPath: string;
  pid: number;
  pidExist: number;
  isPIDExist: boolean;
  serverStart: number;
  serverStarted: boolean;
  isTimeOut: boolean;
  shareLogPath: string;
}

export interface SubmitCheckStateResultGetResultModel {
  result: SubmitCheckStateResultItem[];
}

// P4关注组列表 Model
export interface P4ConcernGroupListItem extends BaseItem {
  name?: string;
  syncUsers?: UserInfoModel[];
  manualUsers?: UserInfoModel[];
  reviewUsers?: UserInfoModel[];
  reviewerIds?: number[];
  memberIds?: number[];
  concernPaths?: P4PermissionListItem[];
  projectID?: number;
  sort?: number;
  memberCount?: number;
  p4Groups?: string[];
  depotID?: number;
  category?: number;
  authoritySort?: number;
  streamID?: number;
  commitType?: number;
  // 仅用于前端
  UUID?: number;
}

// 更新P4关注组干员列表  接口参数
export interface UpdateP4ConcernGroupMemberListParams {
  streamID?: number;
  memberIds: number[];
}

export interface VersionsListItem extends BaseItem {
  name?: string;
  category?: number;
  crossVersion?: boolean;
  depotID?: number;
}

export type VersionsPageParams = BasicPageParams & VersionsListItem;

export type VersionsListGetResultModel = BasicFetchResult<VersionsListItem>;

export interface VersionsItemParams {
  version: VersionsListItem;
}

export interface ValidateDM01StreamExistParams {
  path: string;
  depotID: number;
}

export interface ValidateDM01StreamExistResultModel extends BasicResult {
  isExist: boolean;
}

// id获取DMO1组信息 接口参数
export interface DMO1GroupItemParams {
  dm01StreamGroup: P4ConcernGroupListItem;
}

export interface UserDM01GroupListParams {
  groups: P4ConcernGroupListItem[];
}

// 分支锁列表 Model
export interface StreamLockListItem {
  streamID?: number;
  reviewerIds?: number[];
  reviewerGroups?: string[];
  chatID?: string;
}

// 分支锁列表 接口参数
export type StreamLockPageParams = BasicPageParams & StreamLockListItem;

// 分支锁列表 接口返回数据
export type StreamLockListGetResultModel = BasicFetchResult<StreamLockListItem>;

// P4用户组成员列表 接口返回数据
export interface P4GroupMemberListGetResultModel {
  members: UserInfoModel[];
}

// 通知规则 Model
export interface P4CompleteNoticeListItem extends BaseItem {
  streamID?: number;
  maxMessage?: number;
  name?: string;
  paths?: PathItem[];
  items?: ItemsItem[];
  // 仅用于前端
  UUID?: number;
}

export interface PathItem {
  ruleID?: number;
  path?: string;
  // 前端用
  permit?: number;
}

export interface ItemsItem extends BaseItem {
  ruleID?: number;
  ruleType?: number;
  ruleFile?: string;
  chatID?: string;
}

// 通知规则列表 接口返回数据
export type P4CompleteNoticeListGetResultModel = BasicFetchResult<P4CompleteNoticeListItem>;

// 单个通知规则 接口返回数据
export interface P4CompleteNoticeGetResultModel {
  commitNoticeRule: P4CompleteNoticeListItem;
}

// 白名单列表 Model
export interface P4WhiteListItem {
  ID?: number;
  streamID: number;
  groups: string[];
  members?: UserInfoModel[];
  memberIds: number[];
}
// 白名单列表 接口返回数据
export type WhiteListGetResultModel = BasicFetchResult<P4WhiteListItem>;

export interface WorkspaceMetaFieldsRoleAssignItem {
  role?: string;
  name?: string;
  default_appear?: number;
  deletable?: number;
  member_assign?: number;
  members?: string[];
}
export interface WorkspaceMetaFieldsItem {
  is_required?: number;
  is_visibility?: number;
  role_assign?: WorkspaceMetaFieldsRoleAssignItem[];
  field_name?: string;
  field_key?: string;
  field_alias?: string;
  field_type_key?: string;
  default_value?: any;
  options?: any[];
  compound_fields?: any[];
  is_validity?: number;
  label?: string;
}

export interface WorkspaceProjectFieldsItem {
  field_key?: string;
  field_type_key?: string;
  options?: any[];
  extOptions?: any[];
  compound_fields?: any[];
  field_alias?: string;
  field_name?: string;
  is_custom_field?: boolean;
  is_obsoleted?: boolean;
  work_item_scopes?: any[];
  value_generate_mode?: string;
}

// 可提交单号配置空间字段 Model
export interface CommitParamsWorkspaceFieldsItem {
  metaFields?: WorkspaceMetaFieldsItem[];
  projectFields?: WorkspaceProjectFieldsItem[];
}
// 获取可提交单号配置空间字段 接口返回数据
export interface CommitParamsWorkspaceFieldsGetResultModel {
  data: CommitParamsWorkspaceFieldsItem;
}

export interface CommitParamsConditionParams {
  param_key: string;
  value: any;
  operator: string;
}

export interface CommitParamsCondition {
  conjunction: string;
  search_params: CommitParamsConditionParams[];
  search_groups?: CommitParamsCondition[];
}

// 可提交单号配置 Model
export interface CommitParamsItem extends BaseItem {
  streamID: number;
  workItemTypeKey: 'story' | 'issue';
  params: { search_group: CommitParamsCondition };
}

export interface IssueItem {
  fromStates: string[];
  toState: string | null;
}
export interface StoryItem {
  states: string[];
}
// 可提交单号配置 Model
export interface CommitTransitionItem extends BaseItem {
  streamID?: number;
  story?: StoryItem[];
  issue?: IssueItem[];
}
export interface StateNode {
  name: string;
  state_key: string;
}

export type StateNodes = StateNode[];
// 可提交单号配置 接口参数
export type CommitParamsPageParams = BasicPageParams & Partial<CommitParamsItem>;

// 自动流转配置 接口返回数据
export type CommitTransitionGetResultModel = CommitTransitionItem;
// 节点类型
export type StateNodesGetResultModel = StateNodes;
